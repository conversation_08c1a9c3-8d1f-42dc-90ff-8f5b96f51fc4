<?php

namespace App\Services;

use Omnipay\Omnipay;
use InvalidArgumentException;

class PaymentGatewayFactory
{
    /**
     * Create a payment gateway instance
     *
     * @param string|null $gateway
     * @return \Omnipay\Common\GatewayInterface
     * @throws InvalidArgumentException
     */
    public function create(?string $gateway = null): \Omnipay\Common\GatewayInterface
    {
        $gateway = $gateway ?: config('payments.default');
        
        if (!$this->isGatewayEnabled($gateway)) {
            throw new InvalidArgumentException("Gateway '{$gateway}' is not enabled or configured.");
        }

        $config = config("payments.gateways.{$gateway}");
        
        if (!$config) {
            throw new InvalidArgumentException("Gateway '{$gateway}' is not configured.");
        }

        $omnipayGateway = Omnipay::create($config['driver']);
        $omnipayGateway->initialize($config['options']);

        return $omnipayGateway;
    }

    /**
     * Get all enabled gateways
     *
     * @return array
     */
    public function getEnabledGateways(): array
    {
        $gateways = config('payments.gateways', []);
        
        return array_filter($gateways, function ($config) {
            return $config['enabled'] ?? false;
        });
    }

    /**
     * Check if a gateway is enabled
     *
     * @param string $gateway
     * @return bool
     */
    public function isGatewayEnabled(string $gateway): bool
    {
        $config = config("payments.gateways.{$gateway}");
        
        return $config && ($config['enabled'] ?? false);
    }

    /**
     * Get gateway display name
     *
     * @param string $gateway
     * @return string
     */
    public function getGatewayName(string $gateway): string
    {
        return match ($gateway) {
            'stripe' => 'Stripe',
            'paypal' => 'PayPal',
            'redsys' => 'Redsys',
            default => ucfirst($gateway),
        };
    }

    /**
     * Get supported currencies for a gateway
     *
     * @param string $gateway
     * @return array
     */
    public function getSupportedCurrencies(string $gateway): array
    {
        return match ($gateway) {
            'stripe' => ['EUR', 'USD', 'GBP', 'CAD', 'AUD', 'JPY'],
            'paypal' => ['EUR', 'USD', 'GBP', 'CAD', 'AUD'],
            'redsys' => ['EUR'],
            default => ['EUR', 'USD'],
        };
    }
}
