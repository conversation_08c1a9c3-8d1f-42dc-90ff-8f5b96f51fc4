<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Omnipay\Omnipay;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Registrar las instancias de Omnipay para cada gateway
        $this->app->singleton('omnipay.stripe', function () {
            $gateway = Omnipay::create('Stripe');
            $gateway->initialize(config('payments.gateways.stripe.options'));
            return $gateway;
        });

        $this->app->singleton('omnipay.paypal', function () {
            $gateway = Omnipay::create('PayPal_Express');
            $gateway->initialize(config('payments.gateways.paypal.options'));
            return $gateway;
        });

        // Registrar el factory principal
        $this->app->singleton('omnipay', function () {
            return new \App\Services\PaymentGatewayFactory();
        });

        // Registrar el servicio de pagos
        $this->app->singleton('payment.service', function ($app) {
            return new \App\Services\PaymentService($app->make('omnipay'));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
