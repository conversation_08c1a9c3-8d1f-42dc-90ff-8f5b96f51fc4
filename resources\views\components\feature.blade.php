@props([
    'title',
    'description',
    'image',
    'reverse' => false,
    'logo' => 'media/images/logo.png', // logo por defecto, puedes cambiarlo por parámetro
])

@php
    $layoutClasses = $reverse
        ? 'flex-col md:flex-row-reverse'
        : 'flex-col md:flex-row';

    $textPadding = $reverse
        ? 'md:pr-12'
        : 'md:pl-12';
@endphp

<div class="flex {{ $layoutClasses }} items-stretch px-4 md:px-20">
    <div class="w-full md:w-1/2 mb-8 md:mb-0">
        <img src="{{ asset($image) }}" alt="{{ $title }}" class="w-full h-auto rounded">
    </div>
    <div class="w-full md:w-1/2 {{ $textPadding }} flex flex-col items-center text-center justify-between">
        <div class="w-full flex flex-col items-center">
            <h3 class="text-3xl font-bold uppercase mb-2 text-secondary">{{ $title }}</h3>
            <div class="w-full h-1 bg-white my-3"></div>
            <p class="text-lg mb-6 text-secondary">{{ $description }}</p>
        </div>
        <img src="{{ asset($logo) }}" alt="Logo" class="h-32 mt-auto">
    </div>
</div>
