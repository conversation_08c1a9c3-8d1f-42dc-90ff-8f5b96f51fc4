@props([
    'product',
    'showButton' => true,
    'buttonText' => 'Ver detalles',
])

<div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
    <div class="relative">
            <div class="h-64 bg-gray-200 flex items-center justify-center">
                <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
        <div class="absolute top-0 right-0 bg-blue-600 text-white px-3 py-1 m-2 rounded-full text-sm font-bold">
            Destacado
        </div>
    </div>
    
    <div class="p-6">
        <h3 class="text-2xl font-bold text-gray-900 mb-3">{{ $product->titulo }}</h3>
        <p class="text-gray-600 mb-4">{{ Str::limit($product->descripcion, 150) }}</p>
        
        <div class="flex items-center justify-between">
            <span class="text-3xl font-bold text-blue-600">€{{ number_format($product->precio, 2) }}</span>
            
            @if($showButton)
                <a href="{{ route('products.show', $product->id) }}" 
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    {{ $buttonText }}
                </a>
            @endif
        </div>
        
        @if($product->languages->isNotEmpty())
            <div class="mt-4 flex flex-wrap gap-2">
                @foreach($product->languages as $language)
                    <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $language->name }}</span>
                @endforeach
            </div>
        @endif
    </div>
</div>