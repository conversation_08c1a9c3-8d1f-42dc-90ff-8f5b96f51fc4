<?php if (isset($component)) { $__componentOriginal5a2bca6433522d6f6f2b1706ab627b13 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5a2bca6433522d6f6f2b1706ab627b13 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app-with-cart','data' => ['title' => 'Productos - '.e(config('app.name')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app-with-cart'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Productos - '.e(config('app.name')).'']); ?>
    <!-- Page Content -->
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Nuestros Productos</h1>
                <p class="text-gray-600 mt-2">Descubre nuestra selección de cursos y productos digitales</p>
            </div>

            <!-- Product List Component -->
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-list', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1491464798-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <!-- Listen for cart updates -->
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('addToCart', (event) => {
                // Dispatch cart update event to refresh cart icon
                Livewire.dispatch('cartUpdated');
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5a2bca6433522d6f6f2b1706ab627b13)): ?>
<?php $attributes = $__attributesOriginal5a2bca6433522d6f6f2b1706ab627b13; ?>
<?php unset($__attributesOriginal5a2bca6433522d6f6f2b1706ab627b13); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5a2bca6433522d6f6f2b1706ab627b13)): ?>
<?php $component = $__componentOriginal5a2bca6433522d6f6f2b1706ab627b13; ?>
<?php unset($__componentOriginal5a2bca6433522d6f6f2b1706ab627b13); ?>
<?php endif; ?>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/products/index.blade.php ENDPATH**/ ?>