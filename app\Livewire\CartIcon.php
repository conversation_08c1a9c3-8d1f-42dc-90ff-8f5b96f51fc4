<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Session;
use Livewire\Component;

class CartIcon extends Component
{
    public $count = 0;
    public $total = 0;

    protected $listeners = ['cartUpdated' => 'loadCart'];

    public function mount()
    {
        $this->loadCart();
    }

    public function loadCart()
    {
        $cart = Session::get('cart', []);
        $this->calculateTotals($cart);
    }

    private function calculateTotals($cart)
    {
        $this->count = 0;
        $this->total = 0;

        foreach ($cart as $item) {
            $this->count += $item['quantity'];
            $this->total += $item['precio'] * $item['quantity'];
        }

        $this->total = round($this->total, 2);
    }

    public function render()
    {
        return view('livewire.cart-icon');
    }
}
