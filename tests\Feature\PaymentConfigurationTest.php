<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Services\PaymentGatewayFactory;
use App\Services\PaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentConfigurationTest extends TestCase
{
    use RefreshDatabase;

    public function test_payment_configuration_is_loaded(): void
    {
        $this->assertIsArray(config('payments'));
        $this->assertIsArray(config('payments.gateways'));
        $this->assertNotEmpty(config('payments.default'));
        $this->assertNotEmpty(config('payments.currency'));
    }

    public function test_payment_service_provider_is_registered(): void
    {
        $this->assertTrue($this->app->bound('omnipay'));
        $this->assertTrue($this->app->bound('payment.service'));
    }

    public function test_can_resolve_payment_gateway_factory(): void
    {
        $factory = $this->app->make('omnipay');

        $this->assertInstanceOf(PaymentGatewayFactory::class, $factory);
    }

    public function test_can_resolve_payment_service(): void
    {
        $service = $this->app->make('payment.service');

        $this->assertInstanceOf(PaymentService::class, $service);
    }

    public function test_payment_gateway_factory_methods_work(): void
    {
        /** @var PaymentGatewayFactory $factory */
        $factory = $this->app->make('omnipay');

        // Test gateway names
        $this->assertEquals('Stripe', $factory->getGatewayName('stripe'));
        $this->assertEquals('PayPal', $factory->getGatewayName('paypal'));

        // Test supported currencies
        $stripeCurrencies = $factory->getSupportedCurrencies('stripe');
        $this->assertIsArray($stripeCurrencies);
        $this->assertContains('EUR', $stripeCurrencies);
        $this->assertContains('USD', $stripeCurrencies);

        // Test enabled gateways (should be empty by default since no keys are set)
        $enabledGateways = $factory->getEnabledGateways();
        $this->assertIsArray($enabledGateways);
    }

    public function test_can_create_payment_record(): void
    {
        /** @var PaymentService $paymentService */
        $paymentService = $this->app->make('payment.service');

        $user = User::factory()->create();
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 99.99,
            'estado' => 'pendiente',
            'moneda' => 'EUR',
        ]);

        $payment = $paymentService->createPayment($order, 'stripe');

        $this->assertInstanceOf(Payment::class, $payment);
        $this->assertEquals($order->id, $payment->order_id);
        $this->assertEquals('stripe', $payment->gateway);
        $this->assertEquals(Payment::ESTADO_PENDIENTE, $payment->estado);
        $this->assertEquals(99.99, $payment->monto);
        $this->assertEquals('EUR', $payment->moneda);

        // Verify it's saved in database
        $this->assertDatabaseHas('payments', [
            'order_id' => $order->id,
            'gateway' => 'stripe',
            'estado' => Payment::ESTADO_PENDIENTE,
            'monto' => 99.99,
            'moneda' => 'EUR',
        ]);
    }

    public function test_payment_model_relationships_work(): void
    {
        $user = User::factory()->create();
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 50.00,
            'estado' => 'pendiente',
            'moneda' => 'EUR',
        ]);

        $payment = Payment::create([
            'order_id' => $order->id,
            'gateway' => 'paypal',
            'estado' => Payment::ESTADO_PENDIENTE,
            'monto' => 50.00,
            'moneda' => 'EUR',
        ]);

        // Test relationships
        $this->assertEquals($order->id, $payment->order->id);
        $this->assertEquals($user->id, $payment->order->user->id);
        $this->assertTrue($order->payments->contains($payment));
    }

    public function test_payment_model_scopes_work(): void
    {
        $user = User::factory()->create();
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'estado' => 'pendiente',
            'moneda' => 'EUR',
        ]);

        // Create payments with different states
        Payment::create([
            'order_id' => $order->id,
            'gateway' => 'stripe',
            'estado' => Payment::ESTADO_EXITOSO,
            'monto' => 100.00,
            'moneda' => 'EUR',
        ]);

        Payment::create([
            'order_id' => $order->id,
            'gateway' => 'paypal',
            'estado' => Payment::ESTADO_FALLIDO,
            'monto' => 100.00,
            'moneda' => 'EUR',
        ]);

        Payment::create([
            'order_id' => $order->id,
            'gateway' => 'stripe',
            'estado' => Payment::ESTADO_PENDIENTE,
            'monto' => 100.00,
            'moneda' => 'EUR',
        ]);

        // Test scopes
        $this->assertEquals(1, Payment::exitosos()->count());
        $this->assertEquals(1, Payment::fallidos()->count());
        $this->assertEquals(1, Payment::pendientes()->count());
        $this->assertEquals(2, Payment::gateway('stripe')->count());
    }

    public function test_payment_model_helper_methods_work(): void
    {
        $user = User::factory()->create();
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 75.50,
            'estado' => 'pendiente',
            'moneda' => 'EUR',
        ]);

        $payment = Payment::create([
            'order_id' => $order->id,
            'gateway' => 'stripe',
            'estado' => Payment::ESTADO_EXITOSO,
            'monto' => 75.50,
            'moneda' => 'EUR',
        ]);

        // Test helper methods
        $this->assertTrue($payment->isExitoso());
        $this->assertFalse($payment->isPendiente());
        $this->assertFalse($payment->isFallido());

        // Test formatted attributes
        $this->assertEquals('Stripe', $payment->gateway_nombre);
        $this->assertEquals('Exitoso', $payment->estado_legible);
        $this->assertEquals('75,50 EUR', $payment->monto_formateado);
    }
}
