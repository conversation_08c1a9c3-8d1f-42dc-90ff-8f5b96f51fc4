<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Pedidos
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();

            $table->decimal('total', 10, 2);
            $table->string('estado')->default('pendiente'); // pendiente, pagado, fallido, cancelado
            $table->string('moneda')->default('EUR');
            $table->timestamps();
        });

        // Detalle de cada producto comprado
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->cascadeOnDelete();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();

            $table->integer('cantidad')->default(1);
            $table->decimal('precio_unitario', 10, 2); // copia del precio del producto en el momento
            $table->decimal('subtotal', 10, 2); // cantidad * precio_unitario

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('order_items');
        Schema::dropIfExists('orders');
    }
};
