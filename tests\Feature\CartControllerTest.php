<?php

namespace Tests\Feature;

use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class CartControllerTest extends TestCase
{
    use RefreshDatabase, WithoutMiddleware;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test products
        Product::create([
            'titulo' => 'Test Product 1',
            'descripcion' => 'Test description',
            'precio' => 99.99,
            'activo' => true,
            'slug' => 'test-product-1',
        ]);

        Product::create([
            'titulo' => 'Test Product 2',
            'descripcion' => 'Test description 2',
            'precio' => 149.99,
            'activo' => true,
            'slug' => 'test-product-2',
        ]);
    }

    public function test_can_get_empty_cart(): void
    {
        $response = $this->getJson('/cart/');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'cart' => [],
                'total' => 0,
                'count' => 0,
            ]);
    }

    public function test_can_add_product_to_cart(): void
    {
        $product = Product::first();

        $response = $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 2,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Producto agregado al carrito.',
                'total' => 199.98,
                'count' => 2,
            ]);

        // Verify cart structure
        $cartData = $response->json('cart');
        $this->assertArrayHasKey($product->id, $cartData);
        $this->assertEquals(2, $cartData[$product->id]['quantity']);
        $this->assertEquals($product->precio, $cartData[$product->id]['precio']);
    }

    public function test_cannot_add_inactive_product_to_cart(): void
    {
        $product = Product::create([
            'titulo' => 'Inactive Product',
            'descripcion' => 'Test description',
            'precio' => 50.00,
            'activo' => false,
            'slug' => 'inactive-product',
        ]);

        $response = $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Este producto no está disponible.',
            ]);
    }

    public function test_can_update_cart_item_quantity(): void
    {
        $product = Product::first();

        // Add product to cart first
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);

        // Update quantity
        $response = $this->putJson('/cart/update', [
            'product_id' => $product->id,
            'quantity' => 3,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Carrito actualizado.',
                'total' => 299.97,
                'count' => 3,
            ]);
    }

    public function test_can_remove_product_from_cart(): void
    {
        $product = Product::first();

        // Add product to cart first
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 2,
        ]);

        // Remove product
        $response = $this->deleteJson('/cart/remove', [
            'product_id' => $product->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Producto eliminado del carrito.',
                'cart' => [],
                'total' => 0,
                'count' => 0,
            ]);
    }

    public function test_can_clear_entire_cart(): void
    {
        $product1 = Product::first();
        $product2 = Product::skip(1)->first();

        // Add multiple products
        $this->postJson('/cart/add', ['product_id' => $product1->id, 'quantity' => 1]);
        $this->postJson('/cart/add', ['product_id' => $product2->id, 'quantity' => 2]);

        // Clear cart
        $response = $this->deleteJson('/cart/clear');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Carrito vaciado.',
                'cart' => [],
                'total' => 0,
                'count' => 0,
            ]);
    }

    public function test_can_get_cart_count(): void
    {
        $product = Product::first();

        // Add product to cart
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 3,
        ]);

        $response = $this->getJson('/cart/count');

        $response->assertStatus(200)
            ->assertJson([
                'count' => 3,
            ]);
    }

    public function test_cart_summary_validates_products(): void
    {
        $product = Product::first();

        // Add product to cart
        $this->postJson('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);

        // Get summary
        $response = $this->getJson('/cart/summary');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'has_changes' => false,
            ]);
    }
}
