<form method="POST" action="{{ route('language.set') }}" class="inline-block">
    @csrf
    <input type="hidden" name="redirect" value="{{ request()->fullUrl() }}">

    <select name="locale"
        onchange="this.form.submit()"
        {{ $attributes->merge([
            'class' => 'rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 shadow-sm focus:ring focus:ring-indigo-500 focus:border-indigo-500 transition ease-in-out duration-150 px-3 py-2 text-sm'
        ]) }}>
        @foreach(Mcamara\LaravelLocalization\Facades\LaravelLocalization::getSupportedLocales() as $code => $props)
            <option value="{{ $code }}" {{ app()->getLocale() == $code ? 'selected' : '' }}>
                {{ $props['name'] }}
            </option>
        @endforeach
    </select>
</form>
