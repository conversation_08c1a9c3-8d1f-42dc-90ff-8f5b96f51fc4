<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Models\Payment createPayment(\App\Models\Order $order, ?string $gateway = null, array $options = [])
 * @method static array processPayment(\App\Models\Payment $payment, array $paymentData = [])
 * @method static bool completePayment(\App\Models\Payment $payment, array $data = [])
 * @method static array getAvailableGateways()
 * @method static bool verifyWebhookSignature(string $payload, string $signature, string $gateway)
 * @method static \Omnipay\Common\GatewayInterface create(?string $gateway = null)
 * @method static bool isGatewayEnabled(string $gateway)
 * @method static string getGatewayName(string $gateway)
 * @method static array getSupportedCurrencies(string $gateway)
 *
 * @see \App\Services\PaymentService
 * @see \App\Services\PaymentGatewayFactory
 */
class Payment extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'payment.service';
    }
}
