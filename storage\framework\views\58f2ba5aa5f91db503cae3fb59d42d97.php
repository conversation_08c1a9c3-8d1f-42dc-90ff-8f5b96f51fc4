<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title',
    'description',
    'image',
    'reverse' => false,
    'logo' => 'media/images/logo.png', // logo por defecto, puedes cambiarlo por parámetro
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title',
    'description',
    'image',
    'reverse' => false,
    'logo' => 'media/images/logo.png', // logo por defecto, puedes cambiarlo por parámetro
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $layoutClasses = $reverse
        ? 'flex-col md:flex-row-reverse'
        : 'flex-col md:flex-row';

    $textPadding = $reverse
        ? 'md:pr-12'
        : 'md:pl-12';
?>

<div class="flex <?php echo e($layoutClasses); ?> items-stretch px-4 md:px-20">
    <div class="w-full md:w-1/2 mb-8 md:mb-0">
        <img src="<?php echo e(asset($image)); ?>" alt="<?php echo e($title); ?>" class="w-full h-auto rounded">
    </div>
    <div class="w-full md:w-1/2 <?php echo e($textPadding); ?> flex flex-col items-center text-center justify-between">
        <div class="w-full flex flex-col items-center">
            <h3 class="text-3xl font-bold uppercase mb-2 text-secondary"><?php echo e($title); ?></h3>
            <div class="w-full h-1 bg-white my-3"></div>
            <p class="text-lg mb-6 text-secondary"><?php echo e($description); ?></p>
        </div>
        <img src="<?php echo e(asset($logo)); ?>" alt="Logo" class="h-32 mt-auto">
    </div>
</div>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/components/feature.blade.php ENDPATH**/ ?>