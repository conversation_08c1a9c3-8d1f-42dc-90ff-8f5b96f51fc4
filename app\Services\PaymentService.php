<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Omnipay\Common\Exception\OmnipayException;

class PaymentService
{
    protected PaymentGatewayFactory $gatewayFactory;

    public function __construct(PaymentGatewayFactory $gatewayFactory)
    {
        $this->gatewayFactory = $gatewayFactory;
    }

    /**
     * Create a payment for an order
     *
     * @param Order $order
     * @param string|null $gateway
     * @param array $options
     * @return Payment
     */
    public function createPayment(Order $order, ?string $gateway = null, array $options = []): Payment
    {
        $gateway = $gateway ?: config('payments.default');
        
        return Payment::create([
            'order_id' => $order->id,
            'gateway' => $gateway,
            'estado' => Payment::ESTADO_PENDIENTE,
            'monto' => $order->total,
            'moneda' => $order->moneda,
        ]);
    }

    /**
     * Process a payment
     *
     * @param Payment $payment
     * @param array $paymentData
     * @return array
     */
    public function processPayment(Payment $payment, array $paymentData = []): array
    {
        try {
            $gateway = $this->gatewayFactory->create($payment->gateway);
            
            // Marcar como procesando
            $payment->markAsProcesando();

            $purchaseData = [
                'amount' => $payment->monto,
                'currency' => $payment->moneda,
                'description' => "Order #{$payment->order_id}",
                'returnUrl' => url(config('payments.urls.success')),
                'cancelUrl' => url(config('payments.urls.cancel')),
                'notifyUrl' => url(config('payments.urls.notify')),
            ];

            // Merge con datos adicionales
            $purchaseData = array_merge($purchaseData, $paymentData);

            $response = $gateway->purchase($purchaseData)->send();

            if ($response->isSuccessful()) {
                // Pago completado inmediatamente (raro, pero posible)
                $payment->markAsExitoso(
                    $response->getTransactionReference(),
                    $response->getData()
                );

                return [
                    'success' => true,
                    'completed' => true,
                    'transaction_id' => $response->getTransactionReference(),
                ];
            } elseif ($response->isRedirect()) {
                // Necesita redirección (caso más común)
                $payment->update([
                    'referencia' => $response->getTransactionReference(),
                    'raw_response' => $response->getData(),
                ]);

                return [
                    'success' => true,
                    'completed' => false,
                    'redirect_url' => $response->getRedirectUrl(),
                    'redirect_method' => $response->getRedirectMethod(),
                    'redirect_data' => $response->getRedirectData(),
                ];
            } else {
                // Error en el pago
                $payment->markAsFallido(
                    $response->getMessage() ?: 'Payment failed',
                    $response->getData()
                );

                return [
                    'success' => false,
                    'error' => $response->getMessage() ?: 'Payment failed',
                ];
            }
        } catch (OmnipayException $e) {
            Log::error('Payment processing error', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            $payment->markAsFallido($e->getMessage());

            return [
                'success' => false,
                'error' => 'Payment processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Complete a payment (for callbacks)
     *
     * @param Payment $payment
     * @param array $data
     * @return bool
     */
    public function completePayment(Payment $payment, array $data = []): bool
    {
        try {
            $gateway = $this->gatewayFactory->create($payment->gateway);

            $response = $gateway->completePurchase([
                'transactionReference' => $payment->referencia,
            ])->send();

            if ($response->isSuccessful()) {
                $payment->markAsExitoso(
                    $response->getTransactionReference(),
                    $response->getData()
                );

                // Actualizar estado de la orden
                $payment->order->update(['estado' => 'pagado']);

                return true;
            } else {
                $payment->markAsFallido(
                    $response->getMessage() ?: 'Payment completion failed',
                    $response->getData()
                );

                return false;
            }
        } catch (OmnipayException $e) {
            Log::error('Payment completion error', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            $payment->markAsFallido($e->getMessage());
            return false;
        }
    }

    /**
     * Get available payment gateways
     *
     * @return array
     */
    public function getAvailableGateways(): array
    {
        return $this->gatewayFactory->getEnabledGateways();
    }

    /**
     * Verify webhook signature (for Stripe)
     *
     * @param string $payload
     * @param string $signature
     * @param string $gateway
     * @return bool
     */
    public function verifyWebhookSignature(string $payload, string $signature, string $gateway): bool
    {
        if ($gateway === 'stripe') {
            $secret = config('payments.gateways.stripe.webhook_secret');
            if (!$secret) {
                return false;
            }

            $expectedSignature = hash_hmac('sha256', $payload, $secret);
            return hash_equals($expectedSignature, $signature);
        }

        // Para otros gateways, implementar según sea necesario
        return true;
    }
}
