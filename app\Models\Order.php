<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $fillable = [
        'user_id',
        'total',
        'estado',
        'moneda',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function scopePaid($query)
    {
        return $query->whereHas('payments', function ($query) {
            $query->exitosos();
        });
    }

    public function scopeNotPaid($query)
    {
        return $query->whereDoesntHave('payments', function ($query) {
            $query->exitosos();
        });
    }

    public function scopePending($query)
    {
        return $query->whereHas('payments', function ($query) {
            $query->pendientes();
        });
    }

    public function scopeFailed($query)
    {
        return $query->whereHas('payments', function ($query) {
            $query->fallidos();
        });
    }
}
