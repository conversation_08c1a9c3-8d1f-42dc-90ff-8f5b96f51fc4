<?php $__env->startSection('content'); ?>

<div class="container px-auto">
  <?php if (isset($component)) { $__componentOriginalb9eddf53444261b5c229e9d8b9f1298e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e = $attributes; } ?>
<?php $component = App\View\Components\Navbar::resolve(['brand' => 'Quantum Transition','class' => 'px-4 md:px-20 shadow-lg bg-black fixed top-0 z-50','links' => [
          ['label' => 'Inicio', 'href' => route('home')],
          ['label' => 'Cursos', 'href' => route('products.index')],
          ['label' => 'Sobre Mi', 'href' => '#contacto']
      ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Navbar::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e)): ?>
<?php $attributes = $__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e; ?>
<?php unset($__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb9eddf53444261b5c229e9d8b9f1298e)): ?>
<?php $component = $__componentOriginalb9eddf53444261b5c229e9d8b9f1298e; ?>
<?php unset($__componentOriginalb9eddf53444261b5c229e9d8b9f1298e); ?>
<?php endif; ?>
</div>

<!-- Page Content -->
<div class="py-20">
    <div class="max-w-7xl mx-auto mt-5 sm:px-6 lg:px-8 ">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <h1 class="text-2xl font-bold mb-6">Carrito de Compras</h1>

                <!-- Cart Items -->
                <div id="cart-items" class="mb-8">
                    <div id="empty-cart" class="text-center py-8">
                        <p class="text-gray-600 mb-4">Tu carrito está vacío</p>
                        <a href="<?php echo e(route('products.index')); ?>" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                            Continuar Comprando
                        </a>
                    </div>
                </div>
                
                <!-- Cart Summary -->
                <div id="cart-summary" class="border-t pt-6 hidden">
                    <div class="flex justify-between mb-4">
                        <span class="font-semibold">Total:</span>
                        <span id="cart-total" class="font-bold">$0.00</span>
                    </div>
                    <div class="flex justify-between space-x-4">
                        <button id="clear-cart" class="border border-red-600 text-red-600 px-6 py-2 rounded-lg hover:bg-red-50">
                            Vaciar Carrito
                        </button>
                        <a href="#" id="checkout-button" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                            Proceder al Pago
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<footer class="bg-gray-800 text-gray-200 py-8 mt-12">
  <div class="container mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
    <div>
      <h5 class="font-bold mb-4">Quantum Transition</h5>
      <p>Texto breve sobre la misión o slogan.</p>
    </div>
    <div>
      <h5 class="font-bold mb-4">Enlaces</h5>
      <ul>
        <li><a href="<?php echo e(route('home')); ?>" class="hover:underline">Inicio</a></li>
        <li><a href="<?php echo e(route('products.index')); ?>" class="hover:underline">Productos</a></li>
      </ul>
    </div>
    <div id="contact">
      <h5 class="font-bold mb-4">Contacto</h5>
      <p><EMAIL></p>
      <p>+34 600 000 000</p>
    </div>
  </div>
</footer>

<?php $__env->startPush('scripts'); ?>
<script>
    // Cart functionality
    let cart = {};

    // Load cart on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadCart();
    });

    function loadCart() {
        fetch('/cart/')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cart = data.cart;
                    updateCartDisplay();
                    updateCartTotal(data.total);
                    // Dispatch event to update cart icon
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('cartUpdated');
                    }
                }
            })
            .catch(error => console.error('Error loading cart:', error));
    }

    function updateCartDisplay() {
        const cartItems = document.getElementById('cart-items');
        const emptyCart = document.getElementById('empty-cart');
        const cartSummary = document.getElementById('cart-summary');
        
        // Rest of your cart display logic
    }

    // Clear cart
    document.getElementById('clear-cart')?.addEventListener('click', function() {
        if (confirm('¿Estás seguro de que quieres vaciar el carrito?')) {
            fetch('/cart/clear', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cart = {};
                    updateCartDisplay();
                    updateCartTotal(0);
                    // Dispatch event to update cart icon
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('cartUpdated');
                    }
                }
            })
            .catch(error => console.error('Error clearing cart:', error));
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.landing', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/cart/index.blade.php ENDPATH**/ ?>