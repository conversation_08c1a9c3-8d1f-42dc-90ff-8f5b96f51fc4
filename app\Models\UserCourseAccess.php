<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserCourseAccess extends Model
{
    protected $fillable = ['user_id', 'course_id', 'order_id', 'fecha_acceso', 'expira_en'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function course()
    {
        return $this->belongsTo(Course::class);
    }
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
