<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('titulo');
            $table->text('descripcion')->nullable();
            $table->string('slug')->unique();
            $table->boolean('publicado')->default(true);
            $table->timestamps();
        });

        // Tabla pivote para muchos-a-muchos: product <-> course
        Schema::create('course_product', function (Blueprint $table) {
            $table->foreignId('course_id')->constrained()->cascadeOnDelete();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();
            $table->primary(['course_id', 'product_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('course_product');
        Schema::dropIfExists('courses');
    }
};
