<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Pago <PERSON>oso</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-xl font-semibold text-gray-900">
                            {{ config('app.name', 'Laravel') }}
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <!-- Success Message -->
                        <div class="text-center mb-8">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                                <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <h1 class="text-3xl font-bold text-green-600 mb-2">¡Pago Exitoso!</h1>
                            <p class="text-gray-600">Tu pedido ha sido procesado correctamente.</p>
                        </div>

                        <!-- Order Details -->
                        <div class="border rounded-lg p-6 mb-6">
                            <h2 class="text-xl font-semibold mb-4">Detalles del Pedido</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <p class="text-sm text-gray-600">Número de Pedido</p>
                                    <p class="font-semibold">#{{ $order->id }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Estado</p>
                                    <p class="font-semibold capitalize">{{ $order->estado }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Total</p>
                                    <p class="font-semibold">€{{ number_format($order->total, 2) }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Fecha</p>
                                    <p class="font-semibold">{{ $order->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>

                            <!-- Order Items -->
                            <h3 class="text-lg font-semibold mb-3">Productos Comprados</h3>
                            <div class="space-y-3">
                                @foreach($order->items as $item)
                                    <div class="flex justify-between items-center py-3 border-b last:border-b-0">
                                        <div>
                                            <h4 class="font-medium">{{ $item->product->titulo }}</h4>
                                            <p class="text-sm text-gray-600">Cantidad: {{ $item->cantidad }}</p>
                                            <p class="text-sm text-gray-600">Precio unitario: €{{ number_format($item->precio_unitario, 2) }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold">€{{ number_format($item->subtotal, 2) }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Payment Information -->
                        @if($order->payments->isNotEmpty())
                            <div class="border rounded-lg p-6 mb-6">
                                <h3 class="text-lg font-semibold mb-3">Información de Pago</h3>
                                @foreach($order->payments as $payment)
                                    <div class="flex justify-between items-center py-2">
                                        <div>
                                            <p class="font-medium">{{ $payment->gateway_nombre }}</p>
                                            <p class="text-sm text-gray-600">{{ $payment->estado_legible }}</p>
                                            @if($payment->paid_at)
                                                <p class="text-sm text-gray-600">Pagado: {{ $payment->paid_at->format('d/m/Y H:i') }}</p>
                                            @endif
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold">{{ $payment->monto_formateado }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        <!-- Course Access Information -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-blue-800 mb-2">Acceso a Cursos</h3>
                            <p class="text-blue-700 mb-3">Ya tienes acceso a los siguientes cursos:</p>
                            <ul class="list-disc list-inside text-blue-700">
                                @foreach($order->items as $item)
                                    @foreach($item->product->courses as $course)
                                        <li>{{ $course->titulo }}</li>
                                    @endforeach
                                @endforeach
                            </ul>
                        </div>

                        <!-- Actions -->
                        <div class="text-center space-x-4">
                            <a href="{{ route('dashboard') }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 inline-block">
                                Ir al Dashboard
                            </a>
                            <a href="{{ route('home') }}" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 inline-block">
                                Volver al Inicio
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
