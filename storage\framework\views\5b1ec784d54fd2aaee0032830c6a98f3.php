<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e($title ?? config('app.name', 'Laravel')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <!-- Left side - Logo and main navigation -->
                    <div class="flex items-center">
                        <a href="<?php echo e(route('home.with-cart')); ?>" class="text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200">
                            <?php echo e(config('app.name', 'Laravel')); ?>

                        </a>
                        
                        <!-- Main Navigation Links -->
                        <div class="hidden md:ml-10 md:flex md:items-baseline md:space-x-4">
                            <a href="<?php echo e(route('home.with-cart')); ?>"
                               class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 <?php echo e(request()->routeIs('home.with-cart') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                                Inicio
                            </a>
                            <a href="<?php echo e(route('products.index')); ?>" 
                               class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 <?php echo e(request()->routeIs('products.*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                                Productos
                            </a>
                            <?php if(auth()->guard()->check()): ?>
                                <a href="<?php echo e(route('orders.index')); ?>" 
                                   class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 <?php echo e(request()->routeIs('orders.*') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                                    Mis Pedidos
                                </a>
                                <a href="<?php echo e(route('dashboard')); ?>" 
                                   class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 <?php echo e(request()->routeIs('dashboard') ? 'text-blue-600 bg-blue-50' : ''); ?>">
                                    Dashboard
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Right side - Cart and user menu -->
                    <div class="flex items-center space-x-4">
                        <!-- Shopping Cart Icon -->
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('cart-icon', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-597738458-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        
                        <!-- User Authentication -->
                        <?php if(auth()->guard()->check()): ?>
                            <!-- User Dropdown -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" 
                                        class="flex items-center text-sm rounded-full text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <span class="sr-only">Open user menu</span>
                                    <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">
                                            <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                        </span>
                                    </div>
                                    <span class="ml-2 text-sm font-medium"><?php echo e(Auth::user()->name); ?></span>
                                    <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                <!-- Dropdown Menu -->
                                <div x-show="open" 
                                     @click.away="open = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                    
                                    <a href="<?php echo e(route('dashboard')); ?>" 
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        Dashboard
                                    </a>
                                    <?php if(Route::has('settings.profile')): ?>
                                        <a href="<?php echo e(route('settings.profile')); ?>"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            Configuración
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?php echo e(route('orders.index')); ?>" 
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        Mis Pedidos
                                    </a>
                                    <div class="border-t border-gray-100"></div>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" 
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            Cerrar Sesión
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Guest Links -->
                            <a href="<?php echo e(route('login')); ?>" 
                               class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                                Iniciar Sesión
                            </a>
                            <a href="<?php echo e(route('register')); ?>" 
                               class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm font-medium transition-colors duration-200">
                                Registrarse
                            </a>
                        <?php endif; ?>

                        <!-- Mobile menu button -->
                        <div class="md:hidden">
                            <button type="button" 
                                    x-data="{ open: false }" 
                                    @click="open = !open"
                                    class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                                <span class="sr-only">Open main menu</span>
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main>
            <?php echo e($slot); ?>

        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-12">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="text-center text-sm text-gray-600">
                    <p>&copy; <?php echo e(date('Y')); ?> <?php echo e(config('app.name', 'Laravel')); ?>. Todos los derechos reservados.</p>
                </div>
            </div>
        </footer>
    </div>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    
    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Global cart update listener -->
    <script>
        document.addEventListener('livewire:init', () => {
            // Listen for cart updates and refresh cart icon
            Livewire.on('cartUpdated', () => {
                // Find and refresh the cart icon component
                const cartIcon = document.querySelector('[wire\\:id]');
                if (cartIcon) {
                    const componentId = cartIcon.getAttribute('wire:id');
                    if (Livewire.find(componentId)) {
                        Livewire.find(componentId).call('loadCart');
                    }
                }
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/components/layouts/app-with-cart.blade.php ENDPATH**/ ?>