<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CartController extends Controller
{
    /**
     * Get cart contents
     */
    public function index(): JsonResponse
    {
        $cart = $this->getCart();

        return response()->json([
            'success' => true,
            'cart' => $cart,
            'total' => $this->calculateTotal($cart),
            'count' => $this->getCartCount($cart),
        ]);
    }

    /**
     * Add product to cart
     */
    public function add(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'integer|min:1|max:10',
        ]);

        $product = Product::findOrFail($request->product_id);
        $quantity = $request->quantity ?? 1;

        if (!$product->activo) {
            return response()->json([
                'success' => false,
                'message' => 'Este producto no está disponible.',
            ], 400);
        }

        $cart = $this->getCart();
        $productId = $product->id;

        if (isset($cart[$productId])) {
            $cart[$productId]['quantity'] += $quantity;
        } else {
            $cart[$productId] = [
                'id' => $product->id,
                'titulo' => $product->titulo,
                'precio' => $product->precio,
                'quantity' => $quantity,
                'slug' => $product->slug,
            ];
        }

        // Limit quantity per product
        if ($cart[$productId]['quantity'] > 10) {
            $cart[$productId]['quantity'] = 10;
        }

        $this->saveCart($cart);

        return response()->json([
            'success' => true,
            'message' => 'Producto agregado al carrito.',
            'cart' => $cart,
            'total' => $this->calculateTotal($cart),
            'count' => $this->getCartCount($cart),
        ]);
    }

    /**
     * Update product quantity in cart
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:0|max:10',
        ]);

        $cart = $this->getCart();
        $productId = $request->product_id;

        if (!isset($cart[$productId])) {
            return response()->json([
                'success' => false,
                'message' => 'Producto no encontrado en el carrito.',
            ], 404);
        }

        if ($request->quantity == 0) {
            unset($cart[$productId]);
        } else {
            $cart[$productId]['quantity'] = $request->quantity;
        }

        $this->saveCart($cart);

        return response()->json([
            'success' => true,
            'message' => 'Carrito actualizado.',
            'cart' => $cart,
            'total' => $this->calculateTotal($cart),
            'count' => $this->getCartCount($cart),
        ]);
    }

    /**
     * Remove product from cart
     */
    public function remove(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $cart = $this->getCart();
        $productId = $request->product_id;

        if (!isset($cart[$productId])) {
            return response()->json([
                'success' => false,
                'message' => 'Producto no encontrado en el carrito.',
            ], 404);
        }

        unset($cart[$productId]);
        $this->saveCart($cart);

        return response()->json([
            'success' => true,
            'message' => 'Producto eliminado del carrito.',
            'cart' => $cart,
            'total' => $this->calculateTotal($cart),
            'count' => $this->getCartCount($cart),
        ]);
    }

    /**
     * Clear entire cart
     */
    public function clear(): JsonResponse
    {
        Session::forget('cart');

        return response()->json([
            'success' => true,
            'message' => 'Carrito vaciado.',
            'cart' => [],
            'total' => 0,
            'count' => 0,
        ]);
    }

    /**
     * Get cart count for header display
     */
    public function count(): JsonResponse
    {
        $cart = $this->getCart();

        return response()->json([
            'count' => $this->getCartCount($cart),
        ]);
    }

    /**
     * Get cart from session
     */
    private function getCart(): array
    {
        return Session::get('cart', []);
    }

    /**
     * Save cart to session
     */
    private function saveCart(array $cart): void
    {
        Session::put('cart', $cart);
    }

    /**
     * Calculate total price of cart
     */
    private function calculateTotal(array $cart): float
    {
        $total = 0;

        foreach ($cart as $item) {
            $total += $item['precio'] * $item['quantity'];
        }

        return round($total, 2);
    }

    /**
     * Get total count of items in cart
     */
    private function getCartCount(array $cart): int
    {
        $count = 0;

        foreach ($cart as $item) {
            $count += $item['quantity'];
        }

        return $count;
    }

    /**
     * Get cart summary for checkout
     */
    public function summary(): JsonResponse
    {
        $cart = $this->getCart();

        if (empty($cart)) {
            return response()->json([
                'success' => false,
                'message' => 'El carrito está vacío.',
            ], 400);
        }

        // Verify all products still exist and are active
        $validCart = [];
        $hasChanges = false;

        foreach ($cart as $productId => $item) {
            $product = Product::find($productId);

            if (!$product || !$product->activo) {
                $hasChanges = true;
                continue; // Skip invalid products
            }

            // Update price if it changed
            if ($product->precio != $item['precio']) {
                $item['precio'] = $product->precio;
                $hasChanges = true;
            }

            $validCart[$productId] = $item;
        }

        if ($hasChanges) {
            $this->saveCart($validCart);
        }

        return response()->json([
            'success' => true,
            'cart' => $validCart,
            'total' => $this->calculateTotal($validCart),
            'count' => $this->getCartCount($validCart),
            'has_changes' => $hasChanges,
        ]);
    }
}
