<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TestimonialResource\Pages;
use App\Filament\Resources\TestimonialResource\RelationManagers;
use App\Models\Testimonial;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TestimonialResource extends Resource
{
    protected static ?string $model = Testimonial::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Homepage';

    protected static null|int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nombre')->required(),
                Forms\Components\TextInput::make('cargo'),
                Forms\Components\TextInput::make('empresa'),
                Forms\Components\TextInput::make('ubicacion'),
                Forms\Components\Textarea::make('contenido')->required(),
                Forms\Components\FileUpload::make('imagen'),
                Forms\Components\Toggle::make('publicado')->required(),
                Forms\Components\TextInput::make('orden')->required(),
                Forms\Components\Select::make('language_id')
                    ->relationship('language', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nombre')->sortable(),
                Tables\Columns\TextColumn::make('cargo')->sortable(),
                Tables\Columns\TextColumn::make('empresa')->sortable(),
                Tables\Columns\TextColumn::make('ubicacion')->sortable(),
                Tables\Columns\TextColumn::make('contenido')->sortable()->limit(50),
                Tables\Columns\TextColumn::make('imagen')->sortable(),
                Tables\Columns\ToggleColumn::make('publicado')->sortable(),
                Tables\Columns\TextColumn::make('orden')->sortable()->visible(false),
                Tables\Columns\TextColumn::make('language.name')->sortable(),
            ])
            ->reorderable('orden')
            ->defaultSort('orden')
            ->reorderRecordsTriggerAction(function (Tables\Actions\Action $action, bool $isReordering) {
                return $action->button();
            })
            ->filters([
                Tables\Filters\SelectFilter::make('language')
                    ->label('Idioma')
                    ->relationship('language', 'name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTestimonials::route('/'),
            'create' => Pages\CreateTestimonial::route('/create'),
            'edit' => Pages\EditTestimonial::route('/{record}/edit'),
        ];
    }
}
