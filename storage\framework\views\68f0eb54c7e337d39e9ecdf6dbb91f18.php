<div class="relative">
    <!-- Cart Icon/Button -->
    <button wire:click="toggleCart" class="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
        </svg>
        <!--[if BLOCK]><![endif]--><?php if($count > 0): ?>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                <?php echo e($count); ?>

            </span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </button>

    <!-- Cart Dropdown -->
    <!--[if BLOCK]><![endif]--><?php if($showCart): ?>
        <div class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Carrito de Compras</h3>
                    <button wire:click="toggleCart" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!--[if BLOCK]><![endif]--><?php if(empty($cart)): ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-600">Tu carrito está vacío</p>
                    </div>
                <?php else: ?>
                    <!-- Cart Items -->
                    <div class="max-h-64 overflow-y-auto">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cart; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productId => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between py-3 border-b last:border-b-0">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900"><?php echo e($item['titulo']); ?></h4>
                                    <p class="text-sm text-gray-500">€<?php echo e(number_format($item['precio'], 2)); ?></p>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <button wire:click="updateQuantity(<?php echo e($productId); ?>, <?php echo e($item['quantity'] - 1); ?>)"
                                            class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                        </svg>
                                    </button>

                                    <span class="w-8 text-center text-sm"><?php echo e($item['quantity']); ?></span>

                                    <button wire:click="updateQuantity(<?php echo e($productId); ?>, <?php echo e($item['quantity'] + 1); ?>)"
                                            class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-300">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </button>

                                    <button wire:click="removeFromCart(<?php echo e($productId); ?>)"
                                            class="ml-2 text-red-500 hover:text-red-700">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Cart Footer -->
                    <div class="mt-4 pt-4 border-t">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-base font-semibold">Total:</span>
                            <span class="text-base font-bold">€<?php echo e(number_format($total, 2)); ?></span>
                        </div>

                        <div class="space-y-2">
                            <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                                <a href="<?php echo e(route('checkout.index')); ?>"
                                   class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 block">
                                    Proceder al Checkout
                                </a>
                            <?php else: ?>
                                <a href="<?php echo e(route('login')); ?>"
                                   class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 block">
                                    Iniciar Sesión para Comprar
                                </a>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <button wire:click="clearCart"
                                    wire:confirm="¿Estás seguro de que quieres vaciar el carrito?"
                                    class="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300">
                                Vaciar Carrito
                            </button>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
             class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
             class="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/shopping-cart.blade.php ENDPATH**/ ?>