<?php

namespace App\View\Components;

use Illuminate\View\Component;

class Navbar extends Component
{
    public string $brand;
    public array $links;
    public string $class;

    public function __construct(
        string $brand = 'Quantum Transition',
        array $links = [],
        string $class = ''
    ) {
        $this->brand = $brand;

        $this->links = $links ?: [
            ['label' => 'Servicios', 'href' => '#features'],
            ['label' => 'Equipo', 'href' => '#about'],
            ['label' => 'Contacto', 'href' => '#contact'],
        ];

        $this->class = $class;
    }

    public function render()
    {
        return view('components.navbar');
    }
}
