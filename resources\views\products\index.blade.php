<x-layouts.app-with-cart title="Productos - {{ config('app.name') }}">
    <!-- Page Content -->
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- <PERSON> Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Nuestros Productos</h1>
                <p class="text-gray-600 mt-2">Descubre nuestra selección de cursos y productos digitales</p>
            </div>

            <!-- Product List Component -->
            <livewire:product-list />
        </div>
    </div>

    @push('scripts')
    <!-- Listen for cart updates -->
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('addToCart', (event) => {
                // Dispatch cart update event to refresh cart icon
                Livewire.dispatch('cartUpdated');
            });
        });
    </script>
    @endpush
</x-layouts.app-with-cart>
