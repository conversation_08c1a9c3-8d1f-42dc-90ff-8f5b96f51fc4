<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Payment Gateway
    |--------------------------------------------------------------------------
    |
    | This option controls the default payment gateway that will be used
    | when no specific gateway is requested.
    |
    */

    'default' => env('PAYMENTS_DEFAULT_GATEWAY', 'stripe'),

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | The default currency for all payment transactions.
    |
    */

    'currency' => env('PAYMENTS_DEFAULT_CURRENCY', 'EUR'),

    /*
    |--------------------------------------------------------------------------
    | Payment Gateways
    |--------------------------------------------------------------------------
    |
    | Here you may configure the payment gateways for your application.
    | Each gateway has its own configuration options.
    |
    */

    'gateways' => [

        'stripe' => [
            'driver' => 'Stripe',
            'options' => [
                'apiKey' => env('STRIPE_SECRET_KEY'),
                'publishableKey' => env('STRIPE_PUBLISHABLE_KEY'),
            ],
            'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
            'enabled' => !empty(env('STRIPE_SECRET_KEY')),
        ],

        'paypal' => [
            'driver' => 'PayPal_Express',
            'options' => [
                'username' => env('PAYPAL_CLIENT_ID'),
                'password' => env('PAYPAL_CLIENT_SECRET'),
                'testMode' => env('PAYPAL_SANDBOX', true),
            ],
            'enabled' => !empty(env('PAYPAL_CLIENT_ID')),
        ],

        'redsys' => [
            'driver' => 'Redsys',
            'options' => [
                'merchantCode' => env('REDSYS_MERCHANT_CODE'),
                'terminal' => env('REDSYS_TERMINAL', '001'),
                'secretKey' => env('REDSYS_SECRET_KEY'),
                'testMode' => env('REDSYS_SANDBOX', true),
            ],
            'enabled' => false, // Deshabilitado hasta implementar driver personalizado
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Payment URLs
    |--------------------------------------------------------------------------
    |
    | URLs for payment callbacks and webhooks
    |
    */

    'urls' => [
        'success' => '/payment/success',
        'cancel' => '/payment/cancel',
        'notify' => '/payment/notify',
        'webhook' => '/payment/webhook',
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    |
    | General payment configuration options
    |
    */

    'settings' => [
        'timeout' => 30, // seconds
        'max_retries' => 3,
        'auto_capture' => true,
        'save_cards' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | List of supported currencies for payments
    |
    */

    'currencies' => [
        'EUR' => 'Euro',
        'USD' => 'US Dollar',
        'GBP' => 'British Pound',
        'CAD' => 'Canadian Dollar',
    ],

];
