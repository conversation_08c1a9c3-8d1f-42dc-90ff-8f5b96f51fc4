<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->string('nombre');
            $table->string('archivo'); // ruta al archivo en storage
            $table->integer('orden')->default(0); // opcional, para ordenar descargas
            $table->timestamps();
        });

        // Tabla pivote para muchos-a-muchos: product <-> file
        Schema::create('file_product', function (Blueprint $table) {
            $table->foreignId('file_id')->constrained()->cascadeOnDelete();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();
            $table->primary(['file_id', 'product_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('file_product');
        Schema::dropIfExists('files');
    }
};
