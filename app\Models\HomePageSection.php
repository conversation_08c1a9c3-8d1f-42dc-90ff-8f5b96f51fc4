<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class HomePageSection extends Model
{
    protected $table = 'homepage_sections';

    protected $fillable = [
        'nombre',
        'slug',
        'tipo',
        'slug_categoria',
        'orden',
        'visible',
        'limite',
    ];

    protected $casts = [
        'visible' => 'boolean',
    ];

    public function scopeVisible(Builder $query)
    {
        return $query->where('visible', true);
    }

    public function scopeOrdered(Builder $query)
    {
        return $query->orderBy('orden');
    }
}
