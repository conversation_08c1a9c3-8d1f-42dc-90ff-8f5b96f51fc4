<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Feature extends Model
{
    /** @use HasFactory<\Database\Factories\FeatureFactory> */
    use HasFactory;

    protected $table = 'features';

    protected $fillable = [
        'titulo',
        'contenido',
        'imagen',
        'publicado',
        'orden',
        'language_id',
    ];

    protected $casts = [
        'publicado' => 'boolean',
    ];

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function scopePublicado($query)
    {
        return $query->where('publicado', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('orden');
    }
}
