<form method="POST" action="<?php echo e(route('language.set')); ?>" class="inline-block">
    <?php echo csrf_field(); ?>
    <input type="hidden" name="redirect" value="<?php echo e(request()->fullUrl()); ?>">

    <select name="locale"
        onchange="this.form.submit()"
        <?php echo e($attributes->merge([
            'class' => 'rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 shadow-sm focus:ring focus:ring-indigo-500 focus:border-indigo-500 transition ease-in-out duration-150 px-3 py-2 text-sm'
        ])); ?>>
        <?php $__currentLoopData = Mcamara\LaravelLocalization\Facades\LaravelLocalization::getSupportedLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $props): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <option value="<?php echo e($code); ?>" <?php echo e(app()->getLocale() == $code ? 'selected' : ''); ?>>
                <?php echo e($props['name']); ?>

            </option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
</form>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/components/language-selector.blade.php ENDPATH**/ ?>