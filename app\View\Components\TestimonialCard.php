<?php

namespace App\View\Components;

use Illuminate\View\Component;

class TestimonialCard extends Component
{
    public string $contenido;
    public ?string $imagen;
    public string $nombre;
    public ?string $cargo;
    public ?string $empresa;

    public function __construct(
        string $contenido,
        ?string $imagen = null,
        string $nombre,
        ?string $cargo = null,
        ?string $empresa = null
    ) {
        $this->contenido = $contenido;
        $this->imagen = $imagen;
        $this->nombre = $nombre;
        $this->cargo = $cargo;
        $this->empresa = $empresa;
    }

    public function render()
    {
        return view('components.testimonial-card');
    }
}
