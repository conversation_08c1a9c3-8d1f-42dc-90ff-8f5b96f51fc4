

<?php $__env->startSection('content'); ?>
    <div class="container px-auto">
        <?php if (isset($component)) { $__componentOriginalb9eddf53444261b5c229e9d8b9f1298e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e = $attributes; } ?>
<?php $component = App\View\Components\Navbar::resolve(['brand' => 'Quantum Transition','class' => 'px-4 md:px-20 shadow-lg bg-black fixed top-0 z-50','links' => [
                ['label' => 'Inicio', 'href' => route('home')],
                ['label' => 'Cursos', 'href' => route('products.index')],
                ['label' => 'Sobre Mi', 'href' => '#contacto']
            ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('navbar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Navbar::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e)): ?>
<?php $attributes = $__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e; ?>
<?php unset($__attributesOriginalb9eddf53444261b5c229e9d8b9f1298e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb9eddf53444261b5c229e9d8b9f1298e)): ?>
<?php $component = $__componentOriginalb9eddf53444261b5c229e9d8b9f1298e; ?>
<?php unset($__componentOriginalb9eddf53444261b5c229e9d8b9f1298e); ?>
<?php endif; ?>
    </div>

    <!-- Product Details Section -->
    <section class="py-16">
        <div class="container mx-auto px-4 md:px-20">
            <div class="flex flex-col md:flex-row -mx-4">
                <div class="w-full md:w-1/2 px-4 mb-8 md:mb-0">
                    <img src="<?php echo e('media/images/placeholder.png'); ?>" alt="<?php echo e($product->titulo); ?>" class="w-full h-auto rounded">
                </div>
                <div class="w-full md:w-1/2 px-4">
                    <h2 class="text-3xl font-bold mb-4"><?php echo e($product->titulo); ?></h2>
                    <p class="text-gray-600 mb-6"><?php echo e($product->descripcion); ?></p>
                    <div class="flex items-center mb-6">
                        <span class="text-2xl font-bold text-blue-600">€<?php echo e(number_format($product->precio, 2)); ?></span>
                    </div>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shopping-cart', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1067503137-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.landing', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/products/show.blade.php ENDPATH**/ ?>