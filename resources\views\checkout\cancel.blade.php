<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Pago <PERSON></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-xl font-semibold text-gray-900">
                            {{ config('app.name', 'Laravel') }}
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <!-- Cancel Message -->
                        <div class="text-center mb-8">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                                <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <h1 class="text-3xl font-bold text-yellow-600 mb-2">Pago Cancelado</h1>
                            <p class="text-gray-600">Tu pago ha sido cancelado. No se ha realizado ningún cargo.</p>
                        </div>

                        <!-- Information -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                            <h2 class="text-lg font-semibold text-yellow-800 mb-2">¿Qué pasó?</h2>
                            <ul class="text-yellow-700 space-y-1">
                                <li>• El pago fue cancelado antes de completarse</li>
                                <li>• No se ha realizado ningún cargo a tu método de pago</li>
                                <li>• Tu carrito de compras sigue disponible</li>
                                <li>• Puedes intentar el pago nuevamente cuando desees</li>
                            </ul>
                        </div>

                        <!-- Possible Reasons -->
                        <div class="border rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold mb-3">Posibles Razones</h3>
                            <div class="space-y-2 text-gray-600">
                                <p>• Cancelaste el pago en la página de la pasarela de pago</p>
                                <p>• Hubo un problema con tu método de pago</p>
                                <p>• Se agotó el tiempo de espera durante el proceso</p>
                                <p>• Decidiste no completar la compra</p>
                            </div>
                        </div>

                        <!-- Next Steps -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-blue-800 mb-2">¿Qué puedes hacer ahora?</h3>
                            <ul class="text-blue-700 space-y-1">
                                <li>• Revisar tu carrito de compras</li>
                                <li>• Intentar el pago nuevamente</li>
                                <li>• Contactar con soporte si necesitas ayuda</li>
                                <li>• Explorar otros productos disponibles</li>
                            </ul>
                        </div>

                        <!-- Actions -->
                        <div class="text-center space-x-4">
                            <a href="{{ route('checkout.index') }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 inline-block">
                                Intentar de Nuevo
                            </a>
                            <a href="{{ route('home') }}" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 inline-block">
                                Volver al Inicio
                            </a>
                        </div>

                        <!-- Support Contact -->
                        <div class="text-center mt-8 pt-6 border-t">
                            <p class="text-sm text-gray-600">
                                ¿Necesitas ayuda? 
                                <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800">
                                    Contacta con soporte
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
