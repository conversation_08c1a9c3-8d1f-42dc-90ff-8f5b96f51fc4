<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    /** @use HasFactory<\Database\Factories\TestimonialFactory> */
    use HasFactory;

    protected $table = 'testimonials';

    protected $fillable = [
        'nombre',
        'cargo',
        'empresa',
        'ubicacion',
        'contenido',
        'imagen',
        'publicado',
        'orden',
        'language_id',
    ];

    protected $casts = [
        'publicado' => 'boolean',
    ];

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function scopePublicado($query)
    {
        return $query->where('publicado', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('orden');
    }
}
