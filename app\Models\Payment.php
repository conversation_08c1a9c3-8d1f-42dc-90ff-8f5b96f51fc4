<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'gateway',
        'estado',
        'referencia',
        'transaction_id',
        'monto',
        'moneda',
        'raw_response',
        'error_message',
        'paid_at',
    ];

    protected $casts = [
        'monto' => 'decimal:2',
        'raw_response' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Estados disponibles para el pago
     */
    public const ESTADO_PENDIENTE = 'pendiente';
    public const ESTADO_PROCESANDO = 'procesando';
    public const ESTADO_EXITOSO = 'exitoso';
    public const ESTADO_FALLIDO = 'fallido';
    public const ESTADO_CANCELADO = 'cancelado';
    public const ESTADO_REEMBOLSADO = 'reembolsado';

    /**
     * Gateways disponibles
     */
    public const GATEWAY_STRIPE = 'stripe';
    public const GATEWAY_PAYPAL = 'paypal';
    public const GATEWAY_REDSYS = 'redsys';

    /**
     * Relación con Order
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope para pagos exitosos
     */
    public function scopeExitosos($query)
    {
        return $query->where('estado', self::ESTADO_EXITOSO);
    }

    /**
     * Scope para pagos pendientes
     */
    public function scopePendientes($query)
    {
        return $query->where('estado', self::ESTADO_PENDIENTE);
    }

    /**
     * Scope para pagos fallidos
     */
    public function scopeFallidos($query)
    {
        return $query->where('estado', self::ESTADO_FALLIDO);
    }

    /**
     * Scope por gateway
     */
    public function scopeGateway($query, $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    /**
     * Verifica si el pago fue exitoso
     */
    public function isExitoso(): bool
    {
        return $this->estado === self::ESTADO_EXITOSO;
    }

    /**
     * Verifica si el pago está pendiente
     */
    public function isPendiente(): bool
    {
        return $this->estado === self::ESTADO_PENDIENTE;
    }

    /**
     * Verifica si el pago falló
     */
    public function isFallido(): bool
    {
        return $this->estado === self::ESTADO_FALLIDO;
    }

    /**
     * Marca el pago como exitoso
     */
    public function markAsExitoso(?string $transactionId = null, ?array $rawResponse = null): void
    {
        $this->update([
            'estado' => self::ESTADO_EXITOSO,
            'transaction_id' => $transactionId,
            'raw_response' => $rawResponse,
            'paid_at' => now(),
            'error_message' => null,
        ]);
    }

    /**
     * Marca el pago como fallido
     */
    public function markAsFallido(string $errorMessage, ?array $rawResponse = null): void
    {
        $this->update([
            'estado' => self::ESTADO_FALLIDO,
            'error_message' => $errorMessage,
            'raw_response' => $rawResponse,
        ]);
    }

    /**
     * Marca el pago como procesando
     */
    public function markAsProcesando(?string $referencia = null): void
    {
        $this->update([
            'estado' => self::ESTADO_PROCESANDO,
            'referencia' => $referencia,
        ]);
    }

    /**
     * Obtiene el monto formateado
     */
    public function getMontoFormateadoAttribute(): string
    {
        return number_format($this->monto, 2, ',', '.') . ' ' . $this->moneda;
    }

    /**
     * Obtiene el nombre legible del gateway
     */
    public function getGatewayNombreAttribute(): string
    {
        return match ($this->gateway) {
            self::GATEWAY_STRIPE => 'Stripe',
            self::GATEWAY_PAYPAL => 'PayPal',
            self::GATEWAY_REDSYS => 'Redsys',
            default => ucfirst($this->gateway),
        };
    }

    /**
     * Obtiene el estado legible
     */
    public function getEstadoLegibleAttribute(): string
    {
        return match ($this->estado) {
            self::ESTADO_PENDIENTE => 'Pendiente',
            self::ESTADO_PROCESANDO => 'Procesando',
            self::ESTADO_EXITOSO => 'Exitoso',
            self::ESTADO_FALLIDO => 'Fallido',
            self::ESTADO_CANCELADO => 'Cancelado',
            self::ESTADO_REEMBOLSADO => 'Reembolsado',
            default => ucfirst($this->estado),
        };
    }
}
