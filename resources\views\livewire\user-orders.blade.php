<div>
    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Mis Pedidos</h2>
        <p class="text-gray-600">Historial de tus compras y estado de los pagos</p>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
        <div class="flex flex-wrap gap-2">
            <button wire:click="filterByStatus('')"
                    class="px-4 py-2 rounded-lg text-sm font-medium {{ $filterStatus === '' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Todos
            </button>
            <button wire:click="filterByStatus('pendiente')"
                    class="px-4 py-2 rounded-lg text-sm font-medium {{ $filterStatus === 'pendiente' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Pendientes
            </button>
            <button wire:click="filterByStatus('pagado')"
                    class="px-4 py-2 rounded-lg text-sm font-medium {{ $filterStatus === 'pagado' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Pagados
            </button>
            <button wire:click="filterByStatus('fallido')"
                    class="px-4 py-2 rounded-lg text-sm font-medium {{ $filterStatus === 'fallido' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                Fallidos
            </button>
        </div>
    </div>

    <!-- Orders List -->
    @if($orders->count() > 0)
        <div class="space-y-4 mb-6">
            @foreach($orders as $order)
                <div class="bg-white rounded-lg shadow border hover:shadow-md transition-shadow duration-200">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Pedido #{{ $order->id }}</h3>
                                <p class="text-sm text-gray-600">{{ $order->created_at->format('d/m/Y H:i') }}</p>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->getStatusBadgeClass($order->estado) }}">
                                    {{ ucfirst($order->estado) }}
                                </span>
                                <span class="text-lg font-bold text-gray-900">€{{ number_format($order->total, 2) }}</span>
                            </div>
                        </div>

                        <!-- Order Items Preview -->
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2">
                                @foreach($order->items->take(3) as $item)
                                    <span class="inline-block bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded">
                                        {{ $item->product->titulo }} ({{ $item->cantidad }})
                                    </span>
                                @endforeach
                                @if($order->items->count() > 3)
                                    <span class="inline-block bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded">
                                        +{{ $order->items->count() - 3 }} más
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Payment Status -->
                        @if($order->payments->count() > 0)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Estado del Pago:</h4>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($order->payments as $payment)
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->getPaymentStatusBadgeClass($payment->estado) }}">
                                                {{ $payment->estado_legible }}
                                            </span>
                                            <span class="text-sm text-gray-600">{{ $payment->gateway_nombre }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Actions -->
                        <div class="flex justify-between items-center">
                            <button wire:click="showDetails({{ $order->id }})"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Ver Detalles
                            </button>

                            @if($order->estado === 'pagado')
                                <a href="{{ route('dashboard') }}"
                                   class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                                    Acceder a Cursos
                                </a>
                            @elseif($order->estado === 'pendiente')
                                <a href="{{ route('checkout.index') }}"
                                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                    Completar Pago
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $orders->links() }}
        </div>
    @else
        <!-- No Orders -->
        <div class="text-center py-12 bg-white rounded-lg shadow">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No tienes pedidos</h3>
            <p class="mt-1 text-sm text-gray-500">
                @if($filterStatus)
                    No hay pedidos con estado "{{ $filterStatus }}"
                @else
                    Aún no has realizado ninguna compra.
                @endif
            </p>
            <div class="mt-6">
                @if($filterStatus)
                    <button wire:click="filterByStatus('')"
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Ver Todos los Pedidos
                    </button>
                @else
                    <a href="{{ route('home') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Explorar Productos
                    </a>
                @endif
            </div>
        </div>
    @endif

    <!-- Order Details Modal -->
    @if($showOrderDetails && $selectedOrder)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" wire:click="closeDetails">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" wire:click.stop>
                <!-- Modal Header -->
                <div class="flex items-center justify-between pb-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">Detalles del Pedido #{{ $selectedOrder->id }}</h3>
                    <button wire:click="closeDetails" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="mt-4 max-h-96 overflow-y-auto">
                    <!-- Order Info -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            <p class="text-sm text-gray-600">Fecha del Pedido</p>
                            <p class="font-medium">{{ $selectedOrder->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Estado</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->getStatusBadgeClass($selectedOrder->estado) }}">
                                {{ ucfirst($selectedOrder->estado) }}
                            </span>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total</p>
                            <p class="font-bold text-lg">€{{ number_format($selectedOrder->total, 2) }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Moneda</p>
                            <p class="font-medium">{{ $selectedOrder->moneda }}</p>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="mb-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-3">Productos</h4>
                        <div class="space-y-3">
                            @foreach($selectedOrder->items as $item)
                                <div class="flex justify-between items-center py-3 border-b last:border-b-0">
                                    <div>
                                        <h5 class="font-medium">{{ $item->product->titulo }}</h5>
                                        <p class="text-sm text-gray-600">Cantidad: {{ $item->cantidad }}</p>
                                        <p class="text-sm text-gray-600">Precio unitario: €{{ number_format($item->precio_unitario, 2) }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold">€{{ number_format($item->subtotal, 2) }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Payment Information -->
                    @if($selectedOrder->payments->count() > 0)
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">Información de Pagos</h4>
                            <div class="space-y-3">
                                @foreach($selectedOrder->payments as $payment)
                                    <div class="border rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <div>
                                                <p class="font-medium">{{ $payment->gateway_nombre }}</p>
                                                <p class="text-sm text-gray-600">{{ $payment->monto_formateado }}</p>
                                            </div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $this->getPaymentStatusBadgeClass($payment->estado) }}">
                                                {{ $payment->estado_legible }}
                                            </span>
                                        </div>
                                        @if($payment->paid_at)
                                            <p class="text-sm text-gray-600">Pagado: {{ $payment->paid_at->format('d/m/Y H:i') }}</p>
                                        @endif
                                        @if($payment->transaction_id)
                                            <p class="text-sm text-gray-600">ID Transacción: {{ $payment->transaction_id }}</p>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end pt-4 border-t">
                    <button wire:click="closeDetails"
                            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                        Cerrar
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>
