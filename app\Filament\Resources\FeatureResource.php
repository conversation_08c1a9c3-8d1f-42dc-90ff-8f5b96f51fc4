<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Feature;
use App\Models\Language;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\FeatureResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\FeatureResource\RelationManagers;

class FeatureResource extends Resource
{
    protected static ?string $model = Feature::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Homepage';

    protected static null|int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('titulo')->required(),
                Forms\Components\Textarea::make('contenido')->required(),
                Forms\Components\FileUpload::make('imagen'),
                Forms\Components\Toggle::make('publicado')->required(),
                Forms\Components\TextInput::make('orden')->required(),
                Forms\Components\Select::make('language_id')
                    ->relationship('language', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('titulo')->sortable(),
                Tables\Columns\TextColumn::make('contenido')->sortable(),
                Tables\Columns\TextColumn::make('imagen')->sortable(),
                Tables\Columns\ToggleColumn::make('publicado')->sortable(),
                // Tables\Columns\TextColumn::make('orden')->sortable(),
                Tables\Columns\TextColumn::make('language.name')->sortable(),
            ])
            ->reorderable('orden')
            ->defaultSort('orden')
            ->reorderRecordsTriggerAction(function (Tables\Actions\Action $action, bool $isReordering) {
                return $action->button();
            })
            ->filters([
                Tables\Filters\SelectFilter::make('language')
                    ->label('Idioma')
                    ->relationship('language', 'name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatures::route('/'),
            'create' => Pages\CreateFeature::route('/create'),
            'edit' => Pages\EditFeature::route('/{record}/edit'),
        ];
    }
}
