<x-layouts.app-with-cart title="Mis Pedidos - {{ config('app.name') }}">
    <!-- Page Content -->
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @auth
                <!-- User Orders Component -->
                <livewire:user-orders />
            @else
                <!-- Not Authenticated -->
                <div class="text-center py-12 bg-white rounded-lg shadow">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    <h3 class="mt-2 text-lg font-medium text-gray-900">Acceso <PERSON></h3>
                    <p class="mt-1 text-sm text-gray-500">
                        Necesitas iniciar sesión para ver tus pedidos.
                    </p>
                    <div class="mt-6 space-x-4">
                        <a href="{{ route('login') }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Iniciar Sesión
                        </a>
                        <a href="{{ route('register') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Registrarse
                        </a>
                    </div>
                </div>
            @endauth
        </div>
    </div>
</x-layouts.app-with-cart>
