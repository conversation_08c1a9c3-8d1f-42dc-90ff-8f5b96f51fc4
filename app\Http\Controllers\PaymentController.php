<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Payment;
use App\Models\UserCourseAccess;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    protected PaymentService $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Handle payment success callback
     */
    public function success(Request $request): RedirectResponse
    {
        $paymentId = $request->query('payment_id');
        $transactionRef = $request->query('transaction_reference');

        if (!$paymentId) {
            return redirect()->route('home')
                ->with('error', 'Información de pago no válida.');
        }

        $payment = Payment::find($paymentId);

        if (!$payment) {
            return redirect()->route('home')
                ->with('error', 'Pago no encontrado.');
        }

        // Complete the payment
        $success = $this->paymentService->completePayment($payment, [
            'transaction_reference' => $transactionRef,
        ]);

        if ($success) {
            // Grant course access
            $this->grantCourseAccess($payment->order);

            return redirect()->route('checkout.success', ['order' => $payment->order_id])
                ->with('success', 'Pago procesado exitosamente.');
        } else {
            return redirect()->route('checkout.cancel')
                ->with('error', 'Error procesando el pago.');
        }
    }

    /**
     * Handle payment cancel callback
     */
    public function cancel(Request $request): RedirectResponse
    {
        $paymentId = $request->query('payment_id');

        if ($paymentId) {
            $payment = Payment::find($paymentId);
            if ($payment && $payment->isPendiente()) {
                $payment->update(['estado' => Payment::ESTADO_CANCELADO]);
            }
        }

        return redirect()->route('checkout.cancel')
            ->with('info', 'Pago cancelado.');
    }

    /**
     * Handle payment notification (IPN/webhook)
     */
    public function notify(Request $request): Response
    {
        $gateway = $request->query('gateway', 'stripe');

        Log::info('Payment notification received', [
            'gateway' => $gateway,
            'data' => $request->all(),
        ]);

        try {
            switch ($gateway) {
                case 'stripe':
                    return $this->handleStripeWebhook($request);
                case 'paypal':
                    return $this->handlePayPalIPN($request);
                default:
                    Log::warning('Unknown gateway notification', ['gateway' => $gateway]);
                    return response('Unknown gateway', 400);
            }
        } catch (\Exception $e) {
            Log::error('Payment notification error', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'data' => $request->all(),
            ]);

            return response('Error processing notification', 500);
        }
    }

    /**
     * Handle Stripe webhook
     */
    private function handleStripeWebhook(Request $request): Response
    {
        $payload = $request->getContent();
        $signature = $request->header('Stripe-Signature');

        if (!$this->paymentService->verifyWebhookSignature($payload, $signature, 'stripe')) {
            Log::warning('Invalid Stripe webhook signature');
            return response('Invalid signature', 400);
        }

        $event = json_decode($payload, true);

        if (!$event) {
            return response('Invalid JSON', 400);
        }

        Log::info('Stripe webhook event', ['type' => $event['type']]);

        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handleStripePaymentSuccess($event['data']['object']);
                break;
            case 'payment_intent.payment_failed':
                $this->handleStripePaymentFailed($event['data']['object']);
                break;
            default:
                Log::info('Unhandled Stripe event type', ['type' => $event['type']]);
        }

        return response('OK', 200);
    }

    /**
     * Handle PayPal IPN
     */
    private function handlePayPalIPN(Request $request): Response
    {
        // PayPal IPN verification would go here
        // For now, we'll just log the notification

        Log::info('PayPal IPN received', $request->all());

        $transactionId = $request->input('txn_id');
        $status = $request->input('payment_status');

        if ($transactionId && $status) {
            $payment = Payment::where('transaction_id', $transactionId)->first();

            if ($payment) {
                if ($status === 'Completed') {
                    $payment->markAsExitoso($transactionId, $request->all());
                    $this->grantCourseAccess($payment->order);
                } elseif ($status === 'Failed' || $status === 'Denied') {
                    $payment->markAsFallido('PayPal payment failed: ' . $status, $request->all());
                }
            }
        }

        return response('OK', 200);
    }

    /**
     * Handle successful Stripe payment
     */
    private function handleStripePaymentSuccess(array $paymentIntent): void
    {
        $paymentId = $paymentIntent['metadata']['payment_id'] ?? null;

        if (!$paymentId) {
            Log::warning('Stripe payment success without payment_id', $paymentIntent);
            return;
        }

        $payment = Payment::find($paymentId);

        if (!$payment) {
            Log::warning('Payment not found for Stripe success', ['payment_id' => $paymentId]);
            return;
        }

        if ($payment->isExitoso()) {
            Log::info('Payment already marked as successful', ['payment_id' => $paymentId]);
            return;
        }

        $payment->markAsExitoso($paymentIntent['id'], $paymentIntent);
        $payment->order->update(['estado' => 'pagado']);

        $this->grantCourseAccess($payment->order);

        Log::info('Stripe payment marked as successful', ['payment_id' => $paymentId]);
    }

    /**
     * Handle failed Stripe payment
     */
    private function handleStripePaymentFailed(array $paymentIntent): void
    {
        $paymentId = $paymentIntent['metadata']['payment_id'] ?? null;

        if (!$paymentId) {
            Log::warning('Stripe payment failed without payment_id', $paymentIntent);
            return;
        }

        $payment = Payment::find($paymentId);

        if (!$payment) {
            Log::warning('Payment not found for Stripe failure', ['payment_id' => $paymentId]);
            return;
        }

        $errorMessage = $paymentIntent['last_payment_error']['message'] ?? 'Payment failed';
        $payment->markAsFallido($errorMessage, $paymentIntent);

        Log::info('Stripe payment marked as failed', [
            'payment_id' => $paymentId,
            'error' => $errorMessage,
        ]);
    }

    /**
     * Grant course access after successful payment
     */
    private function grantCourseAccess(Order $order): void
    {
        try {
            DB::beginTransaction();

            foreach ($order->items as $item) {
                $product = $item->product;

                // Grant access to all courses associated with this product
                foreach ($product->courses as $course) {
                    $existingAccess = UserCourseAccess::where([
                        'user_id' => $order->user_id,
                        'course_id' => $course->id,
                    ])->first();

                    if (!$existingAccess) {
                        UserCourseAccess::create([
                            'user_id' => $order->user_id,
                            'course_id' => $course->id,
                            'order_id' => $order->id,
                            'fecha_acceso' => now(),
                            'expira_en' => null, // Lifetime access
                        ]);

                        Log::info('Course access granted', [
                            'user_id' => $order->user_id,
                            'course_id' => $course->id,
                            'order_id' => $order->id,
                        ]);
                    }
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to grant course access', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get payment status (AJAX endpoint)
     */
    public function status(Request $request): JsonResponse
    {
        $paymentId = $request->query('payment_id');

        if (!$paymentId) {
            return response()->json([
                'success' => false,
                'message' => 'Payment ID required',
            ], 400);
        }

        $payment = Payment::find($paymentId);

        if (!$payment) {
            return response()->json([
                'success' => false,
                'message' => 'Payment not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'payment' => [
                'id' => $payment->id,
                'estado' => $payment->estado,
                'estado_legible' => $payment->estado_legible,
                'gateway' => $payment->gateway,
                'gateway_nombre' => $payment->gateway_nombre,
                'monto_formateado' => $payment->monto_formateado,
                'created_at' => $payment->created_at,
                'paid_at' => $payment->paid_at,
            ],
            'order' => [
                'id' => $payment->order->id,
                'estado' => $payment->order->estado,
                'total' => $payment->order->total,
            ],
        ]);
    }
}
