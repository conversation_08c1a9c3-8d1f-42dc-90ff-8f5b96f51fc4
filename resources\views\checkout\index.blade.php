<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Checkout</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-xl font-semibold text-gray-900">
                            {{ config('app.name', 'Laravel') }}
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <h1 class="text-2xl font-bold mb-6">Checkout</h1>

                        <!-- Cart Summary -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold mb-4">Resumen del Pedido</h2>
                            <div class="border rounded-lg p-4">
                                @if(!empty($cart))
                                    @foreach($cart as $item)
                                        <div class="flex justify-between items-center py-2 border-b last:border-b-0">
                                            <div>
                                                <h3 class="font-medium">{{ $item['titulo'] }}</h3>
                                                <p class="text-sm text-gray-600">Cantidad: {{ $item['quantity'] }}</p>
                                            </div>
                                            <div class="text-right">
                                                <p class="font-medium">€{{ number_format($item['precio'] * $item['quantity'], 2) }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                    <div class="pt-4 border-t">
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold">Total:</span>
                                            <span class="text-lg font-bold">€{{ number_format($total, 2) }}</span>
                                        </div>
                                    </div>
                                @else
                                    <p class="text-gray-600">Tu carrito está vacío.</p>
                                @endif
                            </div>
                        </div>

                        <!-- Payment Gateway Selection -->
                        @if(!empty($cart))
                            <div class="mb-8">
                                <h2 class="text-lg font-semibold mb-4">Método de Pago</h2>
                                <form id="checkout-form" method="POST" action="{{ route('checkout.process') }}">
                                    @csrf
                                    
                                    <div class="space-y-4 mb-6">
                                        @foreach($gateways as $gatewayKey => $gateway)
                                            <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                <input type="radio" name="gateway" value="{{ $gatewayKey }}" class="mr-3" required>
                                                <span class="font-medium">{{ ucfirst($gatewayKey) }}</span>
                                            </label>
                                        @endforeach
                                    </div>

                                    <div class="mb-6">
                                        <label class="flex items-center">
                                            <input type="checkbox" name="terms" value="1" class="mr-2" required>
                                            <span class="text-sm">Acepto los términos y condiciones</span>
                                        </label>
                                    </div>

                                    <div class="flex space-x-4">
                                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                                            Procesar Pago
                                        </button>
                                        <a href="{{ route('home') }}" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400">
                                            Cancelar
                                        </a>
                                    </div>
                                </form>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.getElementById('checkout-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    alert(data.message || 'Error procesando el pago');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error procesando el pago');
            });
        });
    </script>
</body>
</html>
