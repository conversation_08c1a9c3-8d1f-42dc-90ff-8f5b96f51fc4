<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Admin',
            'email' => env('ADMIN_EMAIL', '<EMAIL>'),
            'password' => bcrypt(env('ADMIN_PASSWORD', 'password1234567890')),
        ])->assignRole('admin');

        User::create([
            'name' => 'Cliente',
            'email' => env('CLIENT_EMAIL', '<EMAIL>'),
            'password' => bcrypt(env('CLIENT_PASSWORD', 'password1234567890')),
        ])->assignRole('cliente');
    }
}
