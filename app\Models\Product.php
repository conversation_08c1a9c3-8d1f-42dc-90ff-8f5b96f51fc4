<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'titulo',
        'descripcion',
        'precio',
        'activo',
        'slug',
    ];

    public function files()
    {
        return $this->hasMany(File::class);
    }

    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    public function languages()
    {
        return $this->belongsToMany(Language::class, 'pivot_products_language');
    }
}
