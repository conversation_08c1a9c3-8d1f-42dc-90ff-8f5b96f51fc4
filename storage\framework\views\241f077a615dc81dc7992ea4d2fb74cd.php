<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'product',
    'showButton' => true,
    'buttonText' => 'Ver detalles',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'product',
    'showButton' => true,
    'buttonText' => 'Ver detalles',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
    <div class="relative">
            <div class="h-64 bg-gray-200 flex items-center justify-center">
                <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
        <div class="absolute top-0 right-0 bg-blue-600 text-white px-3 py-1 m-2 rounded-full text-sm font-bold">
            Destacado
        </div>
    </div>
    
    <div class="p-6">
        <h3 class="text-2xl font-bold text-gray-900 mb-3"><?php echo e($product->titulo); ?></h3>
        <p class="text-gray-600 mb-4"><?php echo e(Str::limit($product->descripcion, 150)); ?></p>
        
        <div class="flex items-center justify-between">
            <span class="text-3xl font-bold text-blue-600">€<?php echo e(number_format($product->precio, 2)); ?></span>
            
            <?php if($showButton): ?>
                <a href="<?php echo e(route('products.show', $product->id)); ?>" 
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <?php echo e($buttonText); ?>

                </a>
            <?php endif; ?>
        </div>
        
        <?php if($product->languages->isNotEmpty()): ?>
            <div class="mt-4 flex flex-wrap gap-2">
                <?php $__currentLoopData = $product->languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"><?php echo e($language->name); ?></span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
    </div>
</div><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/components/featured-product.blade.php ENDPATH**/ ?>